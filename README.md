# Taskmanager

Flutter application that functions as a project collaboration and management tool. It allows users to create and manage projects, track progress, and stay on top of deadlines with robust notification features. The app sends email notifications when users are added to projects and supports task comments for enhanced collaboration.

## Tools and Technologies
#### Flutter
#### Firebase
#### BLoc
#### push notification
#### Emailjs API
#### Dependency Injection
## UI
### Login and SignUp
<img src="https://github.com/Aliza02/TaskManager/assets/83037489/1d348d5c-0e02-44c5-9812-c76b1b49f20f" height="600px" width="800px">

### Home Screen, Add and Delete Workspace
<img src="https://github.com/Aliza02/TaskManager/assets/83037489/e5de6175-9c66-4cbb-b850-62a5dd09d010" height="600px" width="800px">

### Workspace Details, Add and Delete Tasks
<img src="https://github.com/Aliza02/TaskManager/assets/83037489/0c9cebda-6f2f-40fc-947d-3ce0abfba5a4" height="600px" width="800px">

### All Workspace and Comments
<img src="https://github.com/Aliza02/TaskManager/assets/83037489/cec7adb1-f695-4620-b761-e6186449b3ee" height="600px" width="800px">

### Notifications and Add Members
<img src="https://github.com/Aliza02/TaskManager/assets/83037489/4ddf292a-7e83-4cd8-950d-5ef8cb1499c4" height="600px" width="800px">

